AMP_ENABLE: true
AMP_OPT_LEVEL: ''
AUG:
  AUTO_AUGMENT: rand-m7-mstd0.5-inc1
  COLOR_JITTER: 0.4
  CUTMIX: 0.8
  CUTMIX_MINMAX: null
  MIXUP: 0.6
  MIXUP_MODE: batch
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  RECOUNT: 1
  REMODE: pixel
  REPROB: 0.2
BASE:
- ''
DATA:
  BATCH_SIZE: 128
  CACHE_MODE: part
  DATASET: imagenet
  DATA_PATH: /root/lanyun-fs/imagenet100-split
  IMG_SIZE: 224
  INTERPOLATION: bicubic
  NUM_WORKERS: 4
  PIN_MEMORY: true
  ZIP_MODE: false
ENABLE_AMP: false
EVAL_MODE: false
FUSED_LAYERNORM: false
FUSED_WINDOW_PROCESS: false
LOCAL_RANK: 0
MODEL:
  DROP_PATH_RATE: 0.1
  DROP_RATE: 0.0
  ENABLE_RADIATION: true
  LABEL_SMOOTHING: 0.1
  NAME: vHeat_tiny_with_radiation_imagenet100
  NUM_CLASSES: 100
  PRETRAINED: ''
  RADIATION_STRENGTH: 0.08
  RESUME: ./output_tiny_radiation/vHeat_tiny_with_radiation_imagenet100/vheat_tiny_with_radiation_imagenet100/ckpt_epoch_30.pth
  TYPE: vHeatWithRadiation
  VHEAT:
    DEPTHS:
    - 2
    - 2
    - 6
    - 2
    EMBED_DIM: 96
    IN_CHANS: 3
    LAYER_SCALE: null
    MLP_RATIO: 4.0
    PATCH_NORM: true
    PATCH_SIZE: 4
    POST_NORM: true
OUTPUT: ./output_tiny_radiation/vHeat_tiny_with_radiation_imagenet100/vheat_tiny_with_radiation_imagenet100
PRINT_FREQ: 20
SAVE_FREQ: 10
SEED: 42
TAG: vheat_tiny_with_radiation_imagenet100
TEST:
  CROP: true
  SEQUENTIAL: false
  SHUFFLE: false
THROUGHPUT_MODE: false
TRAIN:
  ACCUMULATION_STEPS: 1
  AUTO_RESUME: true
  BASE_LR: 0.0005
  CLIP_GRAD: 5.0
  EPOCHS: 100
  LAYER_DECAY: 1.0
  LR_SCHEDULER:
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    GAMMA: 0.1
    MULTISTEPS: []
    NAME: cosine
    WARMUP_PREFIX: true
  MIN_LR: 2.5e-07
  MODEL_EMA: false
  MODEL_EMA_DECAY: 0.9999
  MODEL_EMA_FORCE_CPU: false
  MOE:
    SAVE_MASTER: false
  OPTIMIZER:
    BETAS:
    - 0.9
    - 0.999
    EPS: 1.0e-08
    MOMENTUM: 0.9
    NAME: adamw
  START_EPOCH: 0
  USE_CHECKPOINT: false
  WARMUP_EPOCHS: 10
  WARMUP_LR: 2.5e-07
  WEIGHT_DECAY: 0.05
