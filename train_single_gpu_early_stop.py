#!/usr/bin/env python3
"""
单GPU早停训练脚本 - 简化版本，避免分布式训练复杂性
使用方法:
python train_single_gpu_early_stop.py --data-path /path/to/imagenet100 --checkpoint ./output/ckpt_epoch_99.pth
"""

import os
import subprocess
import argparse

def main():
    parser = argparse.ArgumentParser(description='Single GPU training with early stopping')
    parser.add_argument('--data-path', type=str, required=True, 
                        help='Path to ImageNet100 dataset')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to checkpoint file to resume from')
    parser.add_argument('--batch-size', type=int, default=256,
                        help='Batch size (default: 256)')
    parser.add_argument('--output', type=str, default='./output_early_stop',
                        help='Output directory (default: ./output_early_stop)')
    
    # 学习率相关参数
    parser.add_argument('--base-lr', type=float, default=1e-4,
                        help='Base learning rate (default: 1e-4)')
    parser.add_argument('--min-lr', type=float, default=1e-7,
                        help='Minimum learning rate (default: 1e-7)')
    parser.add_argument('--weight-decay', type=float, default=0.02,
                        help='Weight decay (default: 0.02)')
    parser.add_argument('--warmup-epochs', type=int, default=5,
                        help='Warmup epochs (default: 5)')
    
    # 早停相关参数
    parser.add_argument('--patience', type=int, default=15,
                        help='Early stopping patience (default: 15)')
    parser.add_argument('--min-delta', type=float, default=0.0001,
                        help='Minimum improvement threshold (default: 0.0001)')
    
    # 其他参数
    parser.add_argument('--radiation-strength', type=float, default=0.08,
                        help='Radiation strength (default: 0.08)')
    parser.add_argument('--eval-only', action='store_true',
                        help='Only evaluate the model')

    args = parser.parse_args()
    
    # 检查checkpoint文件
    if not os.path.exists(args.checkpoint):
        raise ValueError(f"Checkpoint file does not exist: {args.checkpoint}")
    
    # 检查数据集路径
    if not os.path.exists(args.data_path):
        raise ValueError(f"Dataset path does not exist: {args.data_path}")
    
    train_path = os.path.join(args.data_path, 'train')
    val_path = os.path.join(args.data_path, 'val')
    
    if not os.path.exists(train_path):
        raise ValueError(f"Train directory does not exist: {train_path}")
    if not os.path.exists(val_path):
        raise ValueError(f"Validation directory does not exist: {val_path}")
    
    # 检查类别数
    train_classes = len([d for d in os.listdir(train_path) 
                        if os.path.isdir(os.path.join(train_path, d))])
    
    print(f"Found {train_classes} classes in train set")
    
    # 设置环境变量以支持单GPU训练
    os.environ['RANK'] = '0'
    os.environ['WORLD_SIZE'] = '1'
    os.environ['MASTER_ADDR'] = '127.0.0.1'
    os.environ['MASTER_PORT'] = '29500'
    
    # 使用早停专用配置文件
    config_file = 'classification/configs/vHeat/vHeat_tiny_with_radiation_imagenet100_early_stop.yaml'
    
    # 构建训练命令
    cmd = [
        'python', 'classification/main_with_early_stopping.py',
        '--cfg', config_file,
        '--batch-size', str(args.batch_size),
        '--data-path', args.data_path,
        '--output', args.output,
        '--local_rank', '0'
    ]
    
    # 添加resume参数
    cmd.extend(['--resume', args.checkpoint])
    
    # 添加eval-only参数
    if args.eval_only:
        cmd.append('--eval')

    # 禁用EMA
    cmd.extend(['--model_ema', 'False'])
    
    # 添加早停参数
    cmd.extend(['--early-stopping'])
    cmd.extend(['--patience', str(args.patience)])
    cmd.extend(['--min-delta', str(args.min_delta)])

    # 添加自定义配置选项
    opts = [
        '--opts',
        'TRAIN.BASE_LR', str(args.base_lr),
        'TRAIN.MIN_LR', str(args.min_lr),
        'TRAIN.WEIGHT_DECAY', str(args.weight_decay),
        'TRAIN.WARMUP_EPOCHS', str(args.warmup_epochs),
        'MODEL.NUM_CLASSES', str(train_classes),
        'MODEL.ENABLE_RADIATION', 'True',
        'MODEL.RADIATION_STRENGTH', str(args.radiation_strength),
        'OUTPUT', args.output
    ]

    cmd.extend(opts)
    
    print("=" * 80)
    print("🚀 单GPU早停策略训练配置:")
    print(f"  📁 检查点文件: {args.checkpoint}")
    print(f"  📂 数据集路径: {args.data_path}")
    print(f"  📤 输出目录: {args.output}")
    print(f"  📊 批量大小: {args.batch_size}")
    print(f"  📈 基础学习率: {args.base_lr}")
    print(f"  📉 最小学习率: {args.min_lr}")
    print(f"  ⚖️  权重衰减: {args.weight_decay}")
    print(f"  🔥 预热轮数: {args.warmup_epochs}")
    print(f"  ☢️  辐射强度: {args.radiation_strength}")
    print()
    print("🛑 早停策略配置:")
    print(f"  ⏳ 耐心值: {args.patience} epochs")
    print(f"  📏 最小改善阈值: {args.min_delta} ({args.min_delta*100:.2f}%)")
    print("=" * 80)
    print()
    
    print("🔧 执行命令:")
    print(' '.join(cmd))
    print()
    
    # 执行训练
    try:
        result = subprocess.run(cmd, check=True, env=os.environ.copy())
        print("🎉 早停策略训练完成!")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练失败，错误代码: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("⏹️  训练被用户中断")
        return 1

if __name__ == '__main__':
    exit(main())
